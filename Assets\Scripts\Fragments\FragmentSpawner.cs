using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class FragmentSpawner : MonoBehaviour
{
    [<PERSON><PERSON>("Spawn Settings")]
    [Tooltip("The fragment prefab to spawn")]
    public GameObject fragmentPrefab;

    [Tooltip("How many fragments to spawn per second")]
    public float spawnRate = 2f;

    [Tooltip("Minimum distance from camera edge to spawn fragments")]
    public float minSpawnDistance = 2f;

    [Tooltip("Maximum distance from camera edge to spawn fragments")]
    public float maxSpawnDistance = 5f;

    [<PERSON><PERSON>("Attraction Settings")]
    [Tooltip("The main attractor in the center of the scene")]
    public Transform greatAttractor;

    [Tooltip("Strength of the gravitational pull toward the great attractor")]
    public float attractorGravityStrength = 2f;

    [Tooltip("Tag of orbital objects that can attract fragments")]
    public string orbitalObjectsTag = "Orbital";

    [Tooltip("Strength of the gravitational pull toward orbital objects")]
    public float orbitalGravityStrength = 5f;

    [Tooltip("Distance at which orbital objects start attracting fragments")]
    public float orbitalAttractionRadius = 3f;

    [<PERSON><PERSON><PERSON>("Maximum speed of fragments")]
    public float maxSpeed = 5f;

    [<PERSON><PERSON>("Visual Settings")]
    [Tooltip("Minimum rotation speed of fragments (degrees per second)")]
    public float minRotationSpeed = -30f;

    [Tooltip("Maximum rotation speed of fragments (degrees per second)")]
    public float maxRotationSpeed = 30f;

    [Tooltip("How much to smooth the rotation when changing direction")]
    [Range(0.01f, 0.5f)]
    public float rotationSmoothness = 0.1f;

    [Header("Interaction Settings")]
    [Tooltip("The core animation controller")]
    public CoreAnimation coreAnimation;
    [Tooltip("The object that handles fragment interactions")]
    public FragmentInteractionManager interactionManager;

    private Camera mainCamera;
    private float spawnTimer;
    private List<GameObject> activeFragments = new List<GameObject>();
    private Dictionary<GameObject, float> fragmentRotationSpeeds = new Dictionary<GameObject, float>();
    
    private Dictionary<ScramblerEnemy, float> activeInfluences = new Dictionary<ScramblerEnemy, float>();
    private Dictionary<GameObject, Dictionary<ScramblerEnemy, float>> fragmentInfluences = new Dictionary<GameObject, Dictionary<ScramblerEnemy, float>>();
    private float baseMaxSpeed;
    private float baseGravityStrength;

    void Start()
    {
        mainCamera = Camera.main;
        if (mainCamera == null)
        {
            Debug.LogError("No main camera found in the scene!");
            enabled = false;
            return;
        }

        if (greatAttractor == null)
        {
            Debug.LogError("No Great Attractor assigned!");
            enabled = false;
            return;
        }

        if (fragmentPrefab == null)
        {
            Debug.LogError("No fragment prefab assigned!");
            enabled = false;
            return;
        }
        
        baseMaxSpeed = maxSpeed;
        baseGravityStrength = orbitalGravityStrength;
    }

    void Update()
    {
        spawnTimer += Time.deltaTime;
        if (spawnTimer >= 1f / spawnRate)
        {
            SpawnFragment();
            spawnTimer = 0f;
        }

        UpdateFragments();
    }

    void SpawnFragment()
    {
        Vector3 spawnPosition = GetSpawnPositionOutsideCamera();

        Vector2 initialDirection = (greatAttractor.position - spawnPosition).normalized;
        float initialAngle = Mathf.Atan2(initialDirection.y, initialDirection.x) * Mathf.Rad2Deg;

        GameObject fragment = Instantiate(fragmentPrefab, spawnPosition, Quaternion.Euler(0, 0, initialAngle));

        Rigidbody2D rb = fragment.GetComponent<Rigidbody2D>();
        if (rb == null)
        {
            rb = fragment.AddComponent<Rigidbody2D>();
            rb.gravityScale = 0f;
        }

        rb.velocity = initialDirection * (baseMaxSpeed * 0.5f);

        float rotationSpeed = Random.Range(minRotationSpeed, maxRotationSpeed);
        fragmentRotationSpeeds[fragment] = rotationSpeed;
        
        if (!fragmentInfluences.ContainsKey(fragment))
        {
            fragmentInfluences[fragment] = new Dictionary<ScramblerEnemy, float>();
        }

        activeFragments.Add(fragment);
    }

    Vector3 GetSpawnPositionOutsideCamera()
    {
        float height = 2f * mainCamera.orthographicSize;
        float width = height * mainCamera.aspect;

        int side = Random.Range(0, 4);

        float x = 0f, y = 0f;
        float extraDistance = Random.Range(minSpawnDistance, maxSpawnDistance);

        switch (side)
        {
            case 0:
                x = Random.Range(-width/2, width/2) + mainCamera.transform.position.x;
                y = height/2 + extraDistance + mainCamera.transform.position.y;
                break;
            case 1:
                x = width/2 + extraDistance + mainCamera.transform.position.x;
                y = Random.Range(-height/2, height/2) + mainCamera.transform.position.y;
                break;
            case 2:
                x = Random.Range(-width/2, width/2) + mainCamera.transform.position.x;
                y = -height/2 - extraDistance + mainCamera.transform.position.y;
                break;
            case 3:
                x = -width/2 - extraDistance + mainCamera.transform.position.x;
                y = Random.Range(-height/2, height/2) + mainCamera.transform.position.y;
                break;
        }

        return new Vector3(x, y, greatAttractor.position.z);
    }

    void UpdateFragments()
    {
        GameObject[] orbitalObjects = GameObject.FindGameObjectsWithTag(orbitalObjectsTag);
        
        if (activeInfluences.Count > 0)
        {
            foreach (var fragment in activeFragments)
            {
                if (fragment != null)
                {
                    UpdateFragmentInfluence(fragment);
                }
            }
        }

        for (int i = activeFragments.Count - 1; i >= 0; i--)
        {
            GameObject fragment = activeFragments[i];

            if (fragment == null)
            {
                if (fragmentInfluences.ContainsKey(fragment))
                {
                    fragmentInfluences.Remove(fragment);
                }
                activeFragments.RemoveAt(i);
                continue;
            }

            Rigidbody2D rb = fragment.GetComponent<Rigidbody2D>();
            if (rb == null) continue;

            Vector2 attractorDirection = (greatAttractor.position - fragment.transform.position).normalized;
            Vector2 attractorForce = attractorDirection * attractorGravityStrength;

            bool isNearOrbital = false;
            foreach (GameObject orbitalObj in orbitalObjects)
            {
                float distance = Vector2.Distance(fragment.transform.position, orbitalObj.transform.position);

                if (distance < orbitalAttractionRadius)
                {
                    Vector2 orbitalDirection = (orbitalObj.transform.position - fragment.transform.position).normalized;
                    float gravityMultiplier = 1f - (distance / orbitalAttractionRadius);
                    Vector2 orbitalForce = orbitalDirection * orbitalGravityStrength * gravityMultiplier;

                    attractorForce = orbitalForce;
                    isNearOrbital = true;

                    break;
                }
            }

            rb.AddForce(attractorForce);

            if (rb.velocity.magnitude > maxSpeed)
            {
                rb.velocity = rb.velocity.normalized * maxSpeed;
            }

            if (rb.velocity.magnitude > 0.1f)
            {
                float targetAngle = Mathf.Atan2(rb.velocity.y, rb.velocity.x) * Mathf.Rad2Deg;

                float currentAngle = fragment.transform.eulerAngles.z;

                float newAngle = Mathf.LerpAngle(currentAngle, targetAngle, rotationSmoothness);

                if (fragmentRotationSpeeds.TryGetValue(fragment, out float rotSpeed))
                {
                    newAngle += rotSpeed * Time.deltaTime;
                }

                fragment.transform.rotation = Quaternion.Euler(0, 0, newAngle);
            }

            if (isNearOrbital)
            {
                foreach (GameObject orbitalObj in orbitalObjects)
                {
                    float distance = Vector2.Distance(fragment.transform.position, orbitalObj.transform.position);
                    if (distance < 0.8f)
                    {
                        if (interactionManager != null) interactionManager.OnFragmentAbsorbed(orbitalObj.transform.position);
                        if (coreAnimation != null) coreAnimation.OnFragmentAbsorbed();
                        fragmentRotationSpeeds.Remove(fragment);
                        Destroy(fragment);
                        if (fragmentInfluences.ContainsKey(fragment))
                        {
                            fragmentInfluences.Remove(fragment);
                        }
                        activeFragments.RemoveAt(i);
                        break;
                    }
                }
            }
            else
            {
                float distanceToAttractor = Vector2.Distance(fragment.transform.position, greatAttractor.position);
                if (distanceToAttractor < 0.5f)
                {
                    fragmentRotationSpeeds.Remove(fragment);
                    Destroy(fragment);
                    if (fragmentInfluences.ContainsKey(fragment))
                    {
                        fragmentInfluences.Remove(fragment);
                    }
                    activeFragments.RemoveAt(i);
                }
            }
        }
    }

    public void StartProximityInfluence(ScramblerEnemy scrambler, float duration)
    {
        if (!activeInfluences.ContainsKey(scrambler))
        {
            activeInfluences[scrambler] = 0f;
            UpdateAllFragmentInfluences();
            
            StartCoroutine(RemoveInfluenceAfterDelay(scrambler, duration));
        }
    }
    
    private IEnumerator RemoveInfluenceAfterDelay(ScramblerEnemy scrambler, float delay)
    {
        yield return new WaitForSeconds(delay);
        
        if (activeInfluences.ContainsKey(scrambler))
        {
            activeInfluences.Remove(scrambler);
            
            foreach (var fragment in activeFragments)
            {
                if (fragmentInfluences.ContainsKey(fragment))
                {
                    fragmentInfluences[fragment].Remove(scrambler);
                }
            }
            
            if (!HasActiveInfluences())
            {
                orbitalGravityStrength = baseGravityStrength;
                maxSpeed = baseMaxSpeed;
            }
        }
    }
    
    public void StopProximityInfluence(ScramblerEnemy scrambler)
    {
        if (activeInfluences.Remove(scrambler))
        {
            foreach (var fragment in activeFragments)
            {
                if (fragmentInfluences.ContainsKey(fragment))
                {
                    fragmentInfluences[fragment].Remove(scrambler);
                }
            }
            UpdateAllFragmentInfluences();
        }
    }
    
    public bool HasActiveInfluences()
    {
        return activeInfluences.Count > 0;
    }
    
    private void UpdateAllFragmentInfluences()
    {
        foreach (var fragment in activeFragments)
        {
            if (fragment != null)
            {
                UpdateFragmentInfluence(fragment);
            }
        }
    }
    
    private void UpdateFragmentInfluence(GameObject fragment)
    {
        if (!fragmentInfluences.ContainsKey(fragment))
        {
            fragmentInfluences[fragment] = new Dictionary<ScramblerEnemy, float>();
        }
        
        var influences = fragmentInfluences[fragment];
        influences.Clear();
        
        foreach (var influence in activeInfluences)
        {
            ScramblerEnemy scrambler = influence.Key;
            if (scrambler != null)
            {
                float distance = Vector2.Distance(fragment.transform.position, scrambler.transform.position);
                if (distance <= scrambler.influenceRadius)
                {
                    float influenceStrength = 1f - Mathf.Clamp01(
                        (distance - scrambler.minInfluenceDistance) / 
                        (scrambler.influenceRadius - scrambler.minInfluenceDistance)
                    );
                    influences[scrambler] = influenceStrength;
                }
            }
        }
        
        ApplyInfluenceToFragment(fragment, influences);
    }
    
    private void ApplyInfluenceToFragment(GameObject fragment, Dictionary<ScramblerEnemy, float> influences)
    {
        Rigidbody2D rb = fragment.GetComponent<Rigidbody2D>();
        if (rb == null) return;
        
        float maxInfluence = 0f;
        foreach (var influence in influences.Values)
        {
            if (influence > maxInfluence)
            {
                maxInfluence = influence;
            }
        }
        
        if (maxInfluence > 0)
        {
            ScramblerEnemy strongestInfluence = null;
            float strongestValue = 0f;
            foreach (var kvp in influences)
            {
                if (kvp.Value > strongestValue)
                {
                    strongestValue = kvp.Value;
                    strongestInfluence = kvp.Key;
                }
            }
            
            if (strongestInfluence != null)
            {
                float speedMultiplier = Mathf.Lerp(1f, strongestInfluence.maxFragmentSpeedMultiplier, strongestValue);
                
                if (rb.velocity.magnitude > 0.1f)
                {
                    rb.velocity = rb.velocity.normalized * (baseMaxSpeed * speedMultiplier);
                }
                
                orbitalGravityStrength = Mathf.Lerp(baseGravityStrength, 0f, strongestValue);
            }
        }
        else
        {
            rb.velocity = rb.velocity.normalized * baseMaxSpeed;
            orbitalGravityStrength = baseGravityStrength;
        }
    }
}
