using UnityEngine;

public class CoreAnimation : MonoBehaviour
{
    [<PERSON><PERSON>("Animation Settings")]
    [Tooltip("The scale multiplier when a fragment is absorbed")]
    public float scaleMultiplier = 1.2f;
    
    [Tooltip("Duration of the scale animation in seconds")]
    public float animationDuration = 0.2f;
    
    [Toolt<PERSON>("Curve to control the scale animation")]
    public AnimationCurve scaleCurve = new AnimationCurve(
        new Keyframe(0f, 0f),
        new Keyframe(0.5f, 1f, 0f, -5f),
        new Keyframe(1f, 0f, 0f, 0f)
    );
    
    private Vector3 originalScale;
    private float animationTimer = -1f;
    
    private void Start()
    {
        originalScale = transform.localScale;
    }
    
    private void Update()
    {
        if (animationTimer >= 0f)
        {
            if (animationTimer < animationDuration)
            {
                animationTimer += Time.deltaTime;
                float progress = Mathf.Clamp01(animationTimer / animationDuration);
                
                float curveValue = scaleCurve.Evaluate(progress);
                float scale = 1f + (scaleMultiplier - 1f) * curveValue;
                
                transform.localScale = originalScale * scale;
            }
            else
            {
                animationTimer = -1f;
                transform.localScale = originalScale;
            }
        }
    }
    
    public void OnFragmentAbsorbed()
    {
        if (animationTimer < 0f)
        {
            animationTimer = 0f;
        }
    }
}
