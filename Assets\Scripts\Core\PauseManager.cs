using UnityEngine;
using System.Collections;

public class PauseManager : MonoBehaviour
{
    public static PauseManager Instance { get; private set; }

    [Header("References")]
    [SerializeField] private GameObject pausePanel;
    [SerializeField] private PausePanelAnimation pausePanelAnimation;

    private bool isPaused = false;
    [Header("Input")]
    [SerializeField] private KeyCode pauseKey = KeyCode.Escape;

    private void Awake()
    {
        if (Instance == null)
        {
            if (transform.parent != null)
            {
                transform.SetParent(null);
            }

            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
            return;
        }

        if (pausePanel != null)
        {
            pausePanel.SetActive(false);
        }
    }

    private void Update()
    {
        if (Input.GetKeyDown(pauseKey))
        {
            TogglePause();
        }
    }

    public void TogglePause()
    {
        if (isPaused)
        {
            ResumeGame();
        }
        else
        {
            PauseGame();
        }
    }

    public void PauseGame()
    {
        if (isPaused) return;

        isPaused = true;
        Time.timeScale = 0f;

        if (pausePanel != null && pausePanelAnimation != null)
        {
            // First hide the in-game buttons
            pausePanelAnimation.HideInGameButtons();

            // Then show the pause panel after a short delay to let buttons hide
            StartCoroutine(ShowPausePanelDelayed());
        }
    }

    private IEnumerator ShowPausePanelDelayed()
    {
        // Wait for button hide animation to start
        yield return new WaitForSecondsRealtime(0.1f);

        if (pausePanel != null)
        {
            pausePanel.SetActive(true);
            pausePanelAnimation.ShowPanelWithoutHidingButtons();
        }
    }

    public void ResumeGame()
    {
        if (!isPaused) return;

        isPaused = false;
        Time.timeScale = 1f;

        if (pausePanel != null)
        {
            pausePanelAnimation.HidePanel();
            this.Invoke(() => {
                if (pausePanel != null)
                    pausePanel.SetActive(false);
            }, 0.5f);
        }
    }

    public void QuitToMenu()
    {
        Time.timeScale = 1f;
        Debug.Log("Quit to menu");
    }
}
