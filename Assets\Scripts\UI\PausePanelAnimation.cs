using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

public class PausePanelAnimation : MonoBehaviour
{
    [Header("References")]
    [SerializeField] private CanvasGroup canvasGroup;
    [SerializeField] private RectTransform panelTransform;
    [SerializeField] private Button[] buttons;
    [SerializeField] private float fadeDuration = 0.5f;
    [SerializeField] private float bounceDuration = 0.6f;
    [SerializeField] private float pulseDuration = 2f;

    [Header("In-Game Buttons")]
    [SerializeField] private Button fasterButton;
    [SerializeField] private Button slowerButton;
    [SerializeField] private Button pauseButton;
    [SerializeField] private float inGameButtonAnimDuration = 0.4f;

    private Sequence pulseSequence;
    private Vector3[] buttonOriginalScales;

    private Vector3[] inGameButtonOriginalPositions;
    private Vector3[] inGameButtonOriginalScales;
    private CanvasGroup[] inGameButtonCanvasGroups;

    private void Awake()
    {
        buttonOriginalScales = new Vector3[buttons.Length];
        for (int i = 0; i < buttons.Length; i++)
        {
            buttonOriginalScales[i] = buttons[i].transform.localScale;
        }

        canvasGroup.alpha = 0f;
        panelTransform.localScale = Vector3.zero;

        InitializeInGameButtons();
    }

    private void InitializeInGameButtons()
    {
        Button[] inGameButtons = { fasterButton, slowerButton, pauseButton };
        int validButtonCount = 0;

        foreach (var button in inGameButtons)
        {
            if (button != null) validButtonCount++;
        }

        if (validButtonCount == 0) return;

        inGameButtonOriginalPositions = new Vector3[validButtonCount];
        inGameButtonOriginalScales = new Vector3[validButtonCount];
        inGameButtonCanvasGroups = new CanvasGroup[validButtonCount];

        int index = 0;
        foreach (var button in inGameButtons)
        {
            if (button != null)
            {
                inGameButtonOriginalPositions[index] = button.transform.localPosition;
                inGameButtonOriginalScales[index] = button.transform.localScale;

                CanvasGroup canvasGroup = button.GetComponent<CanvasGroup>();
                if (canvasGroup == null)
                {
                    canvasGroup = button.gameObject.AddComponent<CanvasGroup>();
                }
                inGameButtonCanvasGroups[index] = canvasGroup;

                index++;
            }
        }
    }

    public void ShowPanel()
    {
        DOTween.Kill(panelTransform);
        DOTween.Kill(canvasGroup);

        HideInGameButtons();

        canvasGroup.DOFade(1f, fadeDuration).SetUpdate(true);

        panelTransform.localScale = Vector3.zero;
        panelTransform.DOScale(1.1f, bounceDuration * 0.4f)
            .SetEase(Ease.OutBack)
            .SetUpdate(true)
            .OnComplete(StartPulseAnimation);

        for (int i = 0; i < buttons.Length; i++)
        {
            Button button = buttons[i];
            button.transform.localScale = Vector3.zero;

            button.transform.DOScale(buttonOriginalScales[i], 0.3f)
                .SetDelay(0.1f * i + 0.3f)
                .SetEase(Ease.OutBack)
                .SetUpdate(true);
        }
    }

    public void ShowPanelWithoutHidingButtons()
    {
        DOTween.Kill(panelTransform);
        DOTween.Kill(canvasGroup);

        canvasGroup.DOFade(1f, fadeDuration).SetUpdate(true);

        panelTransform.localScale = Vector3.zero;
        panelTransform.DOScale(1.1f, bounceDuration * 0.4f)
            .SetEase(Ease.OutBack)
            .SetUpdate(true)
            .OnComplete(StartPulseAnimation);

        for (int i = 0; i < buttons.Length; i++)
        {
            Button button = buttons[i];
            button.transform.localScale = Vector3.zero;

            button.transform.DOScale(buttonOriginalScales[i], 0.3f)
                .SetDelay(0.1f * i + 0.3f)
                .SetEase(Ease.OutBack)
                .SetUpdate(true);
        }
    }

    public void HidePanel()
    {
        DOTween.Kill(panelTransform);
        DOTween.Kill(canvasGroup);

        canvasGroup.DOFade(0f, fadeDuration * 0.7f).SetUpdate(true);

        panelTransform.DOScale(0f, fadeDuration * 0.5f)
            .SetEase(Ease.InBack)
            .SetUpdate(true)
            .OnComplete(() => {
                ShowInGameButtons();
            });

        if (pulseSequence != null && pulseSequence.IsActive())
        {
            pulseSequence.Kill();
        }
    }

    private void StartPulseAnimation()
    {
        if (pulseSequence != null && pulseSequence.IsActive())
        {
            pulseSequence.Kill();
        }

        panelTransform.localScale = Vector3.one;

        pulseSequence = DOTween.Sequence()
            .Append(panelTransform.DOScale(1.02f, pulseDuration * 0.5f)
                .SetEase(Ease.InOutSine)
                .SetUpdate(true))
            .Append(panelTransform.DOScale(0.99f, pulseDuration * 0.5f)
                .SetEase(Ease.InOutSine)
                .SetUpdate(true))
            .Append(panelTransform.DOScale(1.01f, pulseDuration * 0.4f)
                .SetEase(Ease.InOutSine)
                .SetUpdate(true))
            .Append(panelTransform.DOScale(1f, pulseDuration * 0.6f)
                .SetEase(Ease.OutSine)
                .SetUpdate(true))
            .SetLoops(-1, LoopType.Restart);

        pulseSequence.SetUpdate(true);
        pulseSequence.Play();
    }

    public void OnButtonHover(int buttonIndex)
    {
        if (buttonIndex >= 0 && buttonIndex < buttons.Length)
        {
            buttons[buttonIndex].transform.DOScale(buttonOriginalScales[buttonIndex] * 1.1f, 0.2f)
                .SetUpdate(true);
        }
    }

    public void OnButtonExit(int buttonIndex)
    {
        if (buttonIndex >= 0 && buttonIndex < buttons.Length)
        {
            buttons[buttonIndex].transform.DOScale(buttonOriginalScales[buttonIndex], 0.2f)
                .SetUpdate(true);
        }
    }

    public void HideInGameButtons()
    {
        if (inGameButtonCanvasGroups == null) return;

        Button[] inGameButtons = { fasterButton, slowerButton, pauseButton };
        int index = 0;

        foreach (var button in inGameButtons)
        {
            if (button != null && index < inGameButtonCanvasGroups.Length)
            {
                DOTween.Kill(button.transform);
                DOTween.Kill(inGameButtonCanvasGroups[index]);

                Sequence hideSequence = DOTween.Sequence();

                Vector3 slideDirection = Vector3.zero;
                if (button == fasterButton)
                    slideDirection = Vector3.right * 100f;
                else if (button == slowerButton)
                    slideDirection = Vector3.left * 100f;
                else if (button == pauseButton)
                    slideDirection = Vector3.up * 100f;

                hideSequence.Append(button.transform.DOLocalMove(
                    inGameButtonOriginalPositions[index] + slideDirection,
                    inGameButtonAnimDuration)
                    .SetEase(Ease.OutQuart)
                    .SetUpdate(true));

                hideSequence.Join(inGameButtonCanvasGroups[index].DOFade(0f, inGameButtonAnimDuration)
                    .SetEase(Ease.OutQuart)
                    .SetUpdate(true));

                hideSequence.Join(button.transform.DOScale(
                    inGameButtonOriginalScales[index] * 0.8f,
                    inGameButtonAnimDuration)
                    .SetEase(Ease.OutQuart)
                    .SetUpdate(true));

                hideSequence.SetDelay(index * 0.05f);

                index++;
            }
        }
    }

    private void ShowInGameButtons()
    {
        if (inGameButtonCanvasGroups == null) return;

        Button[] inGameButtons = { fasterButton, slowerButton, pauseButton };
        int index = 0;

        foreach (var button in inGameButtons)
        {
            if (button != null && index < inGameButtonCanvasGroups.Length)
            {
                DOTween.Kill(button.transform);
                DOTween.Kill(inGameButtonCanvasGroups[index]);

                Sequence showSequence = DOTween.Sequence();

                Vector3 slideDirection = Vector3.zero;
                if (button == fasterButton)
                    slideDirection = Vector3.right * 100f;
                else if (button == slowerButton)
                    slideDirection = Vector3.left * 100f;
                else if (button == pauseButton)
                    slideDirection = Vector3.up * 100f;

                button.transform.localPosition = inGameButtonOriginalPositions[index] + slideDirection;
                inGameButtonCanvasGroups[index].alpha = 0f;
                button.transform.localScale = inGameButtonOriginalScales[index] * 0.8f;

                showSequence.Append(button.transform.DOLocalMove(
                    inGameButtonOriginalPositions[index],
                    inGameButtonAnimDuration)
                    .SetEase(Ease.OutBack)
                    .SetUpdate(true));

                showSequence.Join(inGameButtonCanvasGroups[index].DOFade(1f, inGameButtonAnimDuration)
                    .SetEase(Ease.OutQuart)
                    .SetUpdate(true));

                showSequence.Join(button.transform.DOScale(
                    inGameButtonOriginalScales[index],
                    inGameButtonAnimDuration)
                    .SetEase(Ease.OutBack)
                    .SetUpdate(true));

                showSequence.SetDelay((inGameButtonCanvasGroups.Length - 1 - index) * 0.05f);

                index++;
            }
        }
    }

    private void OnDestroy()
    {
        DOTween.Kill(panelTransform);
        DOTween.Kill(canvasGroup);

        if (pulseSequence != null)
        {
            pulseSequence.Kill();
        }

        if (fasterButton != null) DOTween.Kill(fasterButton.transform);
        if (slowerButton != null) DOTween.Kill(slowerButton.transform);
        if (pauseButton != null) DOTween.Kill(pauseButton.transform);

        if (inGameButtonCanvasGroups != null)
        {
            foreach (var canvasGroup in inGameButtonCanvasGroups)
            {
                if (canvasGroup != null) DOTween.Kill(canvasGroup);
            }
        }
    }
}
