using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

public class PausePanelAnimation : MonoBehaviour
{
    [Header("References")]
    [SerializeField] private CanvasGroup canvasGroup;
    [SerializeField] private RectTransform panelTransform;
    [SerializeField] private Button[] buttons;
    [SerializeField] private float fadeDuration = 0.5f;
    [SerializeField] private float bounceDuration = 0.6f;
    [SerializeField] private float pulseDuration = 2f;

    private Sequence pulseSequence;
    private Vector3[] buttonOriginalScales;

    private void Awake()
    {
        buttonOriginalScales = new Vector3[buttons.Length];
        for (int i = 0; i < buttons.Length; i++)
        {
            buttonOriginalScales[i] = buttons[i].transform.localScale;
        }

        canvasGroup.alpha = 0f;
        panelTransform.localScale = Vector3.zero;
    }

    public void ShowPanel()
    {
        DOTween.Kill(panelTransform);
        DOTween.Kill(canvasGroup);
        canvasGroup.DOFade(1f, fadeDuration).SetUpdate(true);
        
        panelTransform.localScale = Vector3.zero;
        panelTransform.DOScale(1.1f, bounceDuration * 0.4f)
            .SetEase(Ease.OutBack)
            .SetUpdate(true)
            .OnComplete(StartPulseAnimation);
            
        for (int i = 0; i < buttons.Length; i++)
        {
            Button button = buttons[i];
            button.transform.localScale = Vector3.zero;
            
            button.transform.DOScale(buttonOriginalScales[i], 0.3f)
                .SetDelay(0.1f * i + 0.3f)
                .SetEase(Ease.OutBack)
                .SetUpdate(true);
        }
    }

    public void HidePanel()
    {
        DOTween.Kill(panelTransform);
        DOTween.Kill(canvasGroup);
        
        canvasGroup.DOFade(0f, fadeDuration * 0.7f).SetUpdate(true);
        
        panelTransform.DOScale(0f, fadeDuration * 0.5f)
            .SetEase(Ease.InBack)
            .SetUpdate(true);
            
        if (pulseSequence != null && pulseSequence.IsActive())
        if (pulseSequence != null && pulseSequence.IsActive())
        {
            pulseSequence.Kill();
        }
    }

    private void StartPulseAnimation()
    {
        if (pulseSequence != null && pulseSequence.IsActive())
        {
            pulseSequence.Kill();
        }

        panelTransform.localScale = Vector3.one;
        
        pulseSequence = DOTween.Sequence()
            .Append(panelTransform.DOScale(1.02f, pulseDuration * 0.5f)
                .SetEase(Ease.InOutSine)
                .SetUpdate(true))
            .Append(panelTransform.DOScale(0.99f, pulseDuration * 0.5f)
                .SetEase(Ease.InOutSine)
                .SetUpdate(true))
            .Append(panelTransform.DOScale(1.01f, pulseDuration * 0.4f)
                .SetEase(Ease.InOutSine)
                .SetUpdate(true))
            .Append(panelTransform.DOScale(1f, pulseDuration * 0.6f)
                .SetEase(Ease.OutSine)
                .SetUpdate(true))
            .SetLoops(-1, LoopType.Restart);
            
        pulseSequence.SetUpdate(true);
        pulseSequence.Play();
    }

    public void OnButtonHover(int buttonIndex)
    {
        if (buttonIndex >= 0 && buttonIndex < buttons.Length)
        {
            buttons[buttonIndex].transform.DOScale(buttonOriginalScales[buttonIndex] * 1.1f, 0.2f)
                .SetUpdate(true);
        }
    }

    public void OnButtonExit(int buttonIndex)
    {
        if (buttonIndex >= 0 && buttonIndex < buttons.Length)
        {
            buttons[buttonIndex].transform.DOScale(buttonOriginalScales[buttonIndex], 0.2f)
                .SetUpdate(true);
        }
    }

    private void OnDestroy()
    {
        DOTween.Kill(panelTransform);
        DOTween.Kill(canvasGroup);
        
        if (pulseSequence != null)
        {
            pulseSequence.Kill();
        }
    }
}
