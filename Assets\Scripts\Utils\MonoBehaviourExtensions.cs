using UnityEngine;
using System;
using System.Collections;

public static class MonoBehaviourExtensions
{
    public static void Invoke(this MonoBehaviour behaviour, Action action, float delay)
    {
        behaviour.StartCoroutine(InvokeCoroutine(action, delay));
    }

    private static IEnumerator InvokeCoroutine(Action action, float delay)
    {
        yield return new WaitForSecondsRealtime(delay);
        action?.Invoke();
    }
}
